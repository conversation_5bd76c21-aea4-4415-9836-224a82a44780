package com.example.countduck;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;

public class MainActivity extends AppCompatActivity {
    private MyBroadcastReceiverOne one;
    private MyBroadcastReceiverTwo two;
    private MyBroadcastReceiverThree three;
    private ImageView iv_horn;
    private TextView tv_left_content, tv_one, tv_two, tv_three;
    private Handler handler;

    private int num = 0; // 存放序号的变量

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        handler = new Handler(Looper.getMainLooper());
        registerReceivers();
        init();
    }

    private void init() {
        iv_horn = findViewById(R.id.iv_horn);
        tv_left_content = findViewById(R.id.tv_left_content);
        tv_one = findViewById(R.id.tv_one);
        tv_two = findViewById(R.id.tv_two);
        tv_three = findViewById(R.id.tv_three);

        iv_horn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 重置UI和计数器
                tv_left_content.setVisibility(View.VISIBLE);
                iv_horn.setClickable(false);
                tv_one.setVisibility(View.INVISIBLE);
                tv_two.setVisibility(View.INVISIBLE);
                tv_three.setVisibility(View.INVISIBLE);
                tv_one.setText("");
                tv_two.setText("");
                tv_three.setText("");
                num = 0;

                // 发送有序广播
                Intent intent = new Intent("Count_Ducks");
                intent.setPackage(getPackageName()); // 仅本应用内接收
                sendOrderedBroadcast(intent, null);
            }
        });
    }

    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    private void registerReceivers() {
        // 创建接收器实例
        one = new MyBroadcastReceiverOne();
        two = new MyBroadcastReceiverTwo();
        three = new MyBroadcastReceiverThree();

        // 注册广播接收器1 - 最高优先级
        IntentFilter filter1 = new IntentFilter("Count_Ducks");
        filter1.setPriority(1000);
        registerReceiverSafe(one, filter1);

        // 注册广播接收器2 - 中等优先级
        IntentFilter filter2 = new IntentFilter("Count_Ducks");
        filter2.setPriority(900);
        registerReceiverSafe(two, filter2);

        // 注册广播接收器3 - 最低优先级
        IntentFilter filter3 = new IntentFilter("Count_Ducks");
        filter3.setPriority(800);
        registerReceiverSafe(three, filter3);
    }

    // 安全注册方法（适配Android 13+）
    private void registerReceiverSafe(BroadcastReceiver receiver, IntentFilter filter) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.registerReceiver(
                    this,
                    receiver,
                    filter,
                    ContextCompat.RECEIVER_NOT_EXPORTED
            );
        } else {
            registerReceiver(receiver, filter);
        }
    }

    abstract class BaseDuckReceiver extends BroadcastReceiver {
        protected TextView targetView;
        protected String logName;

        public BaseDuckReceiver(TextView targetView, String logName) {
            this.targetView = targetView;
            this.logName = logName;
        }

        @Override
        public void onReceive(Context context, Intent intent) {
            if ("Count_Ducks".equals(intent.getAction())) {
                runOnUiThread(() -> {
                    targetView.setVisibility(View.VISIBLE);
                    num++;
                    targetView.setText(String.valueOf(num));
                });

                Log.i(logName, "接收到了广播消息");
                Toast.makeText(context, logName + ", 接收到了广播消息", Toast.LENGTH_SHORT).show();

                // 延迟执行后续操作
                handler.postDelayed(() -> afterDelay(context), 500);
            }
        }

        protected abstract void afterDelay(Context context);
    }

    class MyBroadcastReceiverOne extends BaseDuckReceiver {
        public MyBroadcastReceiverOne() {
            super(tv_one, "广播接收者One");
        }

        @Override
        protected void afterDelay(Context context) {
            // 不做任何操作，继续传递广播
        }
    }

    class MyBroadcastReceiverTwo extends BaseDuckReceiver {
        public MyBroadcastReceiverTwo() {
            super(tv_two, "广播接收者Two");
        }

        @Override
        protected void afterDelay(Context context) {
            // 此处可以选择中止广播
            // abortBroadcast();
            // Log.i("BroadcastReceiverTwo", "广播消息被我拦截了");
            // Toast.makeText(context, "广播消息被我拦截了", Toast.LENGTH_SHORT).show();
        }
    }

    class MyBroadcastReceiverThree extends BaseDuckReceiver {
        public MyBroadcastReceiverThree() {
            super(tv_three, "广播接收者Three");
        }

        @Override
        protected void afterDelay(Context context) {
            // 所有接收完成后启用喇叭
            runOnUiThread(() -> iv_horn.setClickable(true));
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 注销广播接收器
        try {
            unregisterReceiver(one);
            unregisterReceiver(two);
            unregisterReceiver(three);
        } catch (IllegalArgumentException e) {
            Log.w("MainActivity", "广播接收器未注册");
        }
    }
}