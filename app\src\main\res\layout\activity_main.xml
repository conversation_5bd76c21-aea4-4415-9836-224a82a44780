<?xml version="1.0" encoding="utf-8"?> <RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android" android:layout_width="match_parent" android:layout_height="match_parent" android:background="@drawable/count_ducks_bg">
    <RelativeLayout android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_marginTop="40dp" android:layout_marginLeft="80dp">
        <ImageView

            android:id="@+id/iv_horn" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_centerHorizontal="true" android:src="@drawable/horn" />

        <TextView android:id="@+id/tv_left_content" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_toLeftOf="@id/iv_horn"
            android:background="@drawable/content_left_bg" android:gravity="center"
            android:text="有序报数" android:textColor="@android:color/white" android:visibility="gone"/>
    </RelativeLayout>

    <LinearLayout android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_centerVertical="true" android:gravity="center_horizontal">
        <LinearLayout android:layout_width="wrap_content" android:layout_height="wrap_content" android:orientation="vertical">
            <TextView

                android:id="@+id/tv_one"

                style="@style/badge_style"/> <ImageView
            android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/duck" />
        </LinearLayout> <LinearLayout
        android:layout_width="wrap_content" android:layout_height="wrap_content" android:orientation="vertical">
        <TextView android:id="@+id/tv_two" style="@style/badge_style"/>
        <ImageView

            style="@style/duck_style" />

    </LinearLayout> <LinearLayout
        android:layout_width="wrap_content" android:layout_height="wrap_content" android:orientation="vertical">
        <TextView

            android:id="@+id/tv_three"

            style="@style/badge_style"/> <ImageView
        style="@style/duck_style" />

    </LinearLayout>

    </LinearLayout>

</RelativeLayout>