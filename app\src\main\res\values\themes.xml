<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.Countduck" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Customize your light theme here. -->
        <!-- <item name="colorPrimary">@color/my_light_primary</item> -->
    </style>

    <style name="badge_style">
        <item name="android:layout_width">20dp</item>
        <item name="android:layout_height">20dp</item>
        <item name="android:layout_marginLeft">10dp</item>
        <item name="android:paddingLeft">2dp</item>
        <item name="android:paddingRight">2dp</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/badge_bg</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">12sp</item>
        <item name="android:visibility">gone</item>
    </style>

    <style name="duck_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:src">@drawable/duck</item>
    </style>

    <style name="Theme.Countduck" parent="Base.Theme.Countduck" />
</resources>