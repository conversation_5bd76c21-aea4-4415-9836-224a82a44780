<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">countduck</string>
    <style name="Base.Theme.Countduck" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        
    </style>
    <style name="Theme.Countduck" parent="Base.Theme.Countduck"/>
    <style name="badge_style">
        <item name="android:layout_width">20dp</item>
        <item name="android:layout_height">20dp</item>
        <item name="android:layout_marginLeft">10dp</item>
        <item name="android:paddingLeft">2dp</item>
        <item name="android:paddingRight">2dp</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/badge_bg</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">12sp</item>
        <item name="android:visibility">gone</item>
    </style>
    <style name="duck_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:src">@drawable/duck</item>
    </style>
</resources>