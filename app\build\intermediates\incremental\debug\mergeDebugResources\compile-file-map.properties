#Fri Jul 11 20:57:25 CST 2025
com.example.countduck.app-main-33\:/drawable/badge_bg.xml=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_badge_bg.xml.flat
com.example.countduck.app-main-33\:/drawable/content_left_bg.png=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_content_left_bg.png.flat
com.example.countduck.app-main-33\:/drawable/count_ducks_bg.png=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_count_ducks_bg.png.flat
com.example.countduck.app-main-33\:/drawable/duck.png=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_duck.png.flat
com.example.countduck.app-main-33\:/drawable/horn.png=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_horn.png.flat
com.example.countduck.app-main-33\:/drawable/ic_launcher_background.xml=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.example.countduck.app-main-33\:/drawable/ic_launcher_foreground.xml=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.example.countduck.app-main-33\:/layout/activity_main.xml=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.example.countduck.app-main-33\:/mipmap-anydpi/ic_launcher.xml=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher.xml.flat
com.example.countduck.app-main-33\:/mipmap-anydpi/ic_launcher_round.xml=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher_round.xml.flat
com.example.countduck.app-main-33\:/mipmap-hdpi/ic_launcher.webp=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.countduck.app-main-33\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.countduck.app-main-33\:/mipmap-mdpi/ic_launcher.webp=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.countduck.app-main-33\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.countduck.app-main-33\:/mipmap-xhdpi/ic_launcher.webp=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.countduck.app-main-33\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.countduck.app-main-33\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.countduck.app-main-33\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.countduck.app-main-33\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.countduck.app-main-33\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.countduck.app-main-33\:/xml/backup_rules.xml=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.example.countduck.app-main-33\:/xml/data_extraction_rules.xml=D\:\\16\\countduck\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
