# CountDuck Android应用

一个基于Android广播机制的数鸭子应用。

## 功能特性

- 使用有序广播实现数鸭子功能
- 支持多个广播接收器按优先级顺序处理
- 适配Android 13+的广播接收器注册机制
- 支持日间/夜间主题

## 主要组件

### MainActivity
- 继承自AppCompatActivity，使用AppCompat主题
- 管理三个广播接收器的注册和注销
- 处理UI交互和广播发送

### 广播接收器
- MyBroadcastReceiverOne: 最高优先级(1000)
- MyBroadcastReceiverTwo: 中等优先级(900) 
- MyBroadcastReceiverThree: 最低优先级(800)

## 主题配置

应用使用AppCompat主题系统：
- 日间主题: `Theme.AppCompat.DayNight.NoActionBar`
- 夜间主题: `Theme.AppCompat.DayNight.NoActionBar`
- 自定义样式: `badge_style`, `duck_style`

## 修复记录

### 2025-07-11: 修复主题兼容性问题
- **问题**: 应用启动时崩溃，提示需要使用Theme.AppCompat主题
- **原因**: MainActivity继承AppCompatActivity但使用了Material3主题
- **解决方案**: 
  - 将`Base.Theme.Countduck`的父主题从`Theme.Material3.DayNight.NoActionBar`改为`Theme.AppCompat.DayNight.NoActionBar`
  - 同时更新了日间和夜间主题文件以保持一致性
- **影响文件**:
  - `app/src/main/res/values/themes.xml`
  - `app/src/main/res/values-night/themes.xml`

## 运行要求

- Android API 21+
- 支持Android 13+的新广播注册机制
